<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<view class="back-btn" @tap="goBack">
					<view class="back-icon">
						<view class="back-arrow"></view>
					</view>
				</view>
				<text class="title">群聊列表</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 加载中 -->
			<view class="loading" v-if="loading">
				<view class="loading-spinner"></view>
				<text>加载中...</text>
			</view>

			<!-- 群聊列表 -->
			<view class="group-list" v-else>
				<view class="group-item" v-for="conversation in conversations" :key="conversation.conversationID" 
					@tap="enterGroupChat(conversation)">
					<view class="group-avatar">
						<image :src="conversation.groupProfile?.avatar || '/static/default-group-avatar.png'" 
							mode="aspectFill"></image>
					</view>
					<view class="group-info">
						<view class="group-name">{{ conversation.groupProfile?.name || '群聊' }}</view>
						<view class="last-message">
							<text class="message-text">{{ getLastMessageText(conversation) }}</text>
							<text class="message-time">{{ formatTime(conversation.lastMessage?.lastTime) }}</text>
						</view>
					</view>
					<view class="unread-badge" v-if="conversation.unreadCount > 0">
						<text class="unread-count">{{ conversation.unreadCount > 99 ? '99+' : conversation.unreadCount }}</text>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-state" v-if="conversations.length === 0">
					<image src="/static/icons/empty-chat.svg" mode="aspectFit"></image>
					<text class="empty-text">暂无群聊</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import imManager from "../../utils/IMManager.js"

export default {
	data() {
		return {
			loading: true,
			conversations: []
		}
	},

	onLoad() {
		this.loadGroupList()
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 加载群聊列表
		async loadGroupList() {
			try {
				this.loading = true

				// 检查IM登录状态
				const loginStatus = imManager.getLoginStatus()
				if (!loginStatus.isLoggedIn) {
					uni.showToast({
						title: 'IM未登录',
						icon: 'none'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
					return
				}

				// 获取会话列表
				const result = await imManager.getConversationList()
				if (result.success) {
					// 过滤出群聊会话
					this.conversations = (result.data.conversationList || []).filter(conversation => 
						conversation.type === 'GROUP'
					)
				} else {
					uni.showToast({
						title: '获取群聊列表失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载群聊列表失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 进入群聊
		enterGroupChat(conversation) {
			const groupId = conversation.conversationID.replace('GROUP', '')
			const groupName = conversation.groupProfile?.name || '群聊'
			
			uni.navigateTo({
				url: `/subpages/group-chat/group-chat?groupId=${groupId}&groupName=${encodeURIComponent(groupName)}`
			})
		},

		// 获取最后一条消息文本
		getLastMessageText(conversation) {
			const lastMessage = conversation.lastMessage
			if (!lastMessage) return '暂无消息'

			switch (lastMessage.type) {
				case 'TIMTextElem':
					return lastMessage.payload?.text || '文本消息'
				case 'TIMImageElem':
					return '[图片]'
				case 'TIMSoundElem':
					return '[语音]'
				case 'TIMVideoFileElem':
					return '[视频]'
				case 'TIMFileElem':
					return '[文件]'
				default:
					return '[消息]'
			}
		},

		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return ''

			const now = new Date()
			const messageTime = new Date(timestamp * 1000)
			const diffTime = now.getTime() - messageTime.getTime()
			const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

			if (diffDays === 0) {
				// 今天
				return messageTime.toLocaleTimeString('zh-CN', { 
					hour: '2-digit', 
					minute: '2-digit',
					hour12: false 
				})
			} else if (diffDays === 1) {
				// 昨天
				return '昨天'
			} else if (diffDays < 7) {
				// 一周内
				const weekdays = ['日', '一', '二', '三', '四', '五', '六']
				return '周' + weekdays[messageTime.getDay()]
			} else {
				// 超过一周
				return messageTime.toLocaleDateString('zh-CN', { 
					month: '2-digit', 
					day: '2-digit' 
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;

		.back-icon {
			width: 24rpx;
			height: 24rpx;
			position: relative;

			.back-arrow {
				position: absolute;
				width: 16rpx;
				height: 16rpx;
				border-left: 3rpx solid #fff;
				border-bottom: 3rpx solid #fff;
				transform: rotate(45deg);
				top: 50%;
				left: 50%;
				margin-top: -8rpx;
				margin-left: -8rpx;
			}
		}
	}

	.title {
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
		flex: 1;
		text-align: center;
		margin-right: 80rpx;
	}
}

.page-content {
	padding-top: calc(var(--status-bar-height) + 88rpx);
	flex: 1;
	padding: calc(var(--status-bar-height) + 88rpx) 0 0;
}

.loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #4CAF50;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}

	text {
		color: #666;
		font-size: 28rpx;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.group-list {
	background: #fff;
}

.group-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;

	&:last-child {
		border-bottom: none;
	}

	.group-avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 16rpx;
		overflow: hidden;
		margin-right: 24rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.group-info {
		flex: 1;

		.group-name {
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
			margin-bottom: 8rpx;
		}

		.last-message {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.message-text {
				font-size: 26rpx;
				color: #999;
				flex: 1;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.message-time {
				font-size: 24rpx;
				color: #ccc;
				margin-left: 20rpx;
			}
		}
	}

	.unread-badge {
		position: absolute;
		top: 20rpx;
		right: 30rpx;
		background: #ff4757;
		border-radius: 20rpx;
		min-width: 32rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.unread-count {
			font-size: 20rpx;
			color: #fff;
			font-weight: bold;
			padding: 0 6rpx;
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;

	image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.5;
	}

	.empty-text {
		color: #999;
		font-size: 28rpx;
	}
}

/* 状态栏适配 */
.status-bar {
	height: var(--status-bar-height);
	width: 100%;
}
</style>
